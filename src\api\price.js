import { sendSoapRequest } from "./request.js";

/**
 * 物价查询API
 * 根据物价接口文档实现物价查询功能
 */

/**
 * 物价查询 (9100)
 * @param {Object} params - 查询参数
 * @param {string} params.alias - 收费项目别名 (默认: MR)
 * @param {string} params.tradeCode - 交易代码 (默认: 9100)
 * @returns {Promise<Object>} 物价查询结果
 */
export async function queryPrice(params = {}) {
  try {
    const { alias = "MR", tradeCode = "9100" } = params;

    // 别名是必填参数
    if (!alias) {
      return {
        success: false,
        error: "参数错误",
        message: "收费项目别名不能为空",
      };
    }

    // 构建请求消息XML
    const requestMessage = `<Request><Alias>${alias}</Alias><TradeCode>${tradeCode}</TradeCode></Request>`;

    console.log("物价查询请求参数:", params);

    // 发送SOAP请求
    const result = await sendSoapRequest("MES0061", requestMessage);

    if (result.success) {
      console.log("物价查询成功:", result.data);
      return {
        success: true,
        data: result.data,
        message: "物价查询成功",
      };
    } else {
      console.error("物价查询失败:", result.error);
      return {
        success: false,
        error: result.error,
        message: result.message || "物价查询失败",
      };
    }
  } catch (error) {
    console.error("物价查询异常:", error);
    return {
      success: false,
      error: "查询异常",
      message: error.message || "物价查询过程中发生异常",
    };
  }
}

// 导出所有API方法
export default {
  queryPrice,
};
